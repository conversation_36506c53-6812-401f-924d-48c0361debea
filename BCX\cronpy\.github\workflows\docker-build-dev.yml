name: <PERSON>uild and Push Docker Image (Dev)

on:
  push:
    branches: [ "dev" ]

jobs:
  build_and_push:
    name: Build and Push to DockerHub
    runs-on: ubuntu-latest
    permissions:
      contents: read
    
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4
      
      - name: Log in to Docker Hub
        uses: docker/login-action@f4ef78c080cd8ba55a85445d5b36e214a81df20a
        with:
          username: ${{ secrets.DOCKERHUB_MARCEL_USER }}
          password: ${{ secrets.DOCKERHUB_MARCEL_PUSH }}
      
      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ secrets.DOCKER_USERNAME }}/cronpy
          tags: |
            type=raw,value=dev
            type=sha,format=short
      
      - name: Build and push Docker image
        uses: docker/build-push-action@3b5e8027fcad23fda98b2e3ac259d8d67585f671
        with:
          context: .
          dockerfile: ./docker/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
