services:
  cronicle:
    # image: bluet/cronicle-docker:latest
    # image: soulteary/cronicle:0.9.63
    build:
      context: .
      dockerfile: ./docker/Dockerfile
    hostname: localhost
    container_name: cronpy
    restart: unless-stopped
    network_mode: "host"
    ports:
      - 3012:3012
    environment:
      - TZ=Africa/Johannesburg
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - /var/run/docker.sock:/var/run/docker.sock
      - ./data:/opt/cronicle/data
      - ./logs:/opt/cronicle/logs
      - ./plugins:/opt/cronicle/plugins
      - ~/repos/cpsops:/pyscripts/cpsops
      - ~/data/cronicle:/backups
      # - ./workloads:/app
      # - ./pyscripts:/pyscripts
    # extra_hosts:
    #   - "cronicle.lab.io:0.0.0.0"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider localhost:3012/api/app/ping || exit 1"]
      interval: 5s
      timeout: 1s
      retries: 3
    logging:
        driver: "json-file"
        options:
            max-size: "10m"
